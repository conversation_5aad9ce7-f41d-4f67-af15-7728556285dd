@echo off
:: <PERSON><PERSON><PERSON> đường dẫn thư mục Downloads của người dùng hiện tại
set "download_dir=%USERPROFILE%\Downloads"

:: Tên file cần sao chép
set "source_file=install.bat"

:: Ki<PERSON>m tra xem file nguồn có tồn tại không
if not exist "%~dp0%source_file%" (
    echo Source file %source_file% does not exist in the current directory!
    pause
    exit /b
)

:: Sao chép file vào thư mục Downloads
echo Copying %source_file% to %download_dir%...
copy "%~dp0%source_file%" "%download_dir%" >nul
if %errorlevel%==0 (
    echo File copied successfully!
) else (
    echo Failed to copy the file!
    pause
    exit /b
)

:: Khởi chạy file install.bat trong thư mục Downloads
echo Running install.bat from %download_dir%...
start "" "%download_dir%\install.bat"

:: Thông báo hoàn thành
echo Script executed successfully!
timeout /t 3 >nul
exit
