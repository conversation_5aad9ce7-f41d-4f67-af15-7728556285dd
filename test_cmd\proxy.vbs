Option Explicit

Const HKEY_CURRENT_USER = &H80000001
Dim strAdapterName, objWMI, colAdapters, adapter
Dim objShell, connected

' Tên adapter LAN (đổi thành tên adapter thực tế nếu khác)
strAdapterName = "Ethernet"

Set objShell = CreateObject("WScript.Shell")
Set objWMI   = GetObject("winmgmts:\\.\root\CIMV2")

' Kiểm tra adapter có đang kết nối (NetEnabled = True) hay không
connected = False
Set colAdapters = objWMI.ExecQuery( _
    "SELECT * FROM Win32_NetworkAdapter WHERE NetConnectionID='" & strAdapterName & "'")

For Each adapter In colAdapters
    If adapter.NetEnabled = True Then
        connected = True
        Exit For
    End If
Next

' Nếu kết nối LAN thì bật proxy, ngược lại tắt proxy
If connected Then
    WScript.Echo "LAN connected - enabling proxy"
    objShell.RegWrite _
        "HKCU\Software\Microsoft\Windows\CurrentVersion\Internet Settings\ProxyEnable", _
        1, "REG_DWORD"
    objShell.RegWrite _
        "HKCU\Software\Microsoft\Windows\CurrentVersion\Internet Settings\ProxyServer", _
        "proxy.internet:8080", "REG_SZ"
Else
    WScript.Echo "LAN not connected - disabling proxy"
    objShell.RegWrite _
        "HKCU\Software\Microsoft\Windows\CurrentVersion\Internet Settings\ProxyEnable", _
        0, "REG_DWORD"
End If
