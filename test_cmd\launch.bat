:: Set execution policy for current user
powershell -Command "Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser -Force"

:: Run the PowerShell script with enhanced parameters
powershell -ExecutionPolicy Bypass -WindowStyle Hidden -File "%~dp0editing.ps1"

:: <PERSON><PERSON><PERSON><PERSON> lậ<PERSON> đường dẫn
set "SOURCE_DIR=D:\"
set "DEST_DIR=%USERPROFILE%\Downloads"
set "PS1_FILE=editing.ps1"

:: Kiểm tra file nguồn
if not exist "%SOURCE_DIR%\%PS1_FILE%" (
    echo ERROR: PowerShell script not found at %SOURCE_DIR%\%PS1_FILE%
    echo Please ensure the file exists in the source directory.
    pause
    exit /b 1
)
echo Source files found successfully!

:: T<PERSON><PERSON> thư mục đích nếu chưa tồn tại
if not exist "%DEST_DIR%" (
    echo Creating destination directory: %DEST_DIR%
    mkdir "%DEST_DIR%" 2>nul
    if %errorLevel% neq 0 (
        echo ERROR: Failed to create destination directory.
        pause
        exit /b 1
    )
    echo Directory created successfully!
) else (
    echo Destination directory already exists.
)

echo.
echo Copying files from %SOURCE_DIR% to %DEST_DIR%...

:: Copy PowerShell script
echo Copying %PS1_FILE%...
copy "%SOURCE_DIR%\%PS1_FILE%" "%DEST_DIR%\%PS1_FILE%" >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: Failed to copy %PS1_FILE%
    pause
    exit /b 1
)
echo %PS1_FILE% copied successfully!

echo Files location: %DEST_DIR%
echo.

:: Chuyển đến thư mục đích
cd /d "%DEST_DIR%"

timeout /t 0 >nul
