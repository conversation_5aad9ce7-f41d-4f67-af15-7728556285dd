@echo off
title Auto Setup Script 1.0
mode con: cols=80 lines=25
color 0A
setlocal EnableDelayedExpansion

:: Ki<PERSON>m tra nếu file đang chạy bằng quyền Administrator
	net session >nul 2>&1
	if %errorLevel% neq 0 (
		echo This script needs Administrator privileges.
		echo Requesting elevated privileges...
		powershell -Command "Start-Process '%~0' -Verb RunAs"
		exit
	)

:: <PERSON><PERSON><PERSON><PERSON> trạng thái (dùng để xác định "Run All Options")
	set runall=0

:: <PERSON><PERSON> ch<PERSON>h
:menu
cls
echo.
echo.
echo           ============================================================
echo                               WELCOME TO BAOPROVIP
echo           ============================================================
echo.
echo           [1] Run All Options
echo           [2] Install All Software
echo           [3] Power Options and Firewall
echo           [4] Change The Drive Letter / Edit Volume
echo           [5] Activate Windows 10 Pro / Office 2019 Pro Plus
echo           [6] Turn On Features (Install .NET Framework, Remove IE11)
echo           [7] Rename Device
echo           [8] Set Password
echo           [9] Join Domain
echo           [a] Enable/Disable Startup Programs
echo.
echo           ------------------------------------------------------------
echo           [0] Exit
echo           ------------------------------------------------------------
set /p choice=          Enter a menu option on the Keyboard [1 - 0]:

	if "%choice%"=="1" goto selectdevice
	if "%choice%"=="2" goto install_all
	if "%choice%"=="3" goto fire_wall
	if "%choice%"=="4" goto disk
	if "%choice%"=="5" goto activate
	if "%choice%"=="6" goto turnon_features
	if "%choice%"=="7" goto renamedevice
	if "%choice%"=="8" goto setpassword
	if "%choice%"=="9" goto join_domain
	if "%choice%"=="a" goto startup
	if "%choice%"=="0" goto exit
	echo Invalid option. Please try again!
	pause
	goto menu

:: Chọn thiết bị
:selectdevice
cls
echo            ===========================================================
echo                                SELECT DEVICE TYPE
echo            ===========================================================
echo            [1] Desktop
echo            [2] Laptop
echo            -----------------------------------------------------------
echo            [0] Return to Menu
echo            ===========================================================
	set /p devicetype=Choose your device type [1-2-0]:
	if "%devicetype%"=="1" (
		set runall=1
		goto runall_desktop
	)
	if "%devicetype%"=="2" (
		set runall=1
		goto runall_laptop
	)
	if "%devicetype%"=="0" (
		set runall=1
		goto menu
	)
	echo Invalid choice, please try again!
	pause
	goto selectdevice

:runall_desktop
	echo Running all options for Desktop...
	call :activate_windows
	call :settimezone
	call :install_desktop
	call :activate_office
	call :turnon_features
	call :renamedevice
	call :setpassword
	echo All tasks for Desktop completed successfully!
	echo You may need to restart manually to complete!
	set runall=0
	pause
	goto menu

:runall_laptop
	echo Running all options for Laptop...
	call :activate_windows
	call :settimezone
	call :shrink
	call :install_laptop
	call :activate_office
	call :turnon_features
	call :renamedevice
	call :setpassword
	echo All tasks for Laptop completed successfully!
	echo You may need to restart manually to complete!
	set runall=0
	pause
	goto menu

:activate
cls
echo            ===========================================================
echo                 Activating Windows 10 Pro / Office 2019 Pro Plus
echo            ===========================================================
echo            [1] Active Windows 10 Pro
echo            [2] Active Office2019
echo            [3] Win10Home to Win10Pro
echo            -----------------------------------------------------------
echo            [0] Return to Menu
echo            ===========================================================
	set /p devicetype=Choose your device type [1-2-0]:
	if "%devicetype%"=="1" (
		goto activate_windows
	)
	if "%devicetype%"=="2" (
		goto activate_office
	)
	if "%devicetype%"=="3" (
		goto changeVersion
	)
	if "%devicetype%"=="0" (
		goto menu
	)
    echo Invalid choice, please try again!
	pause
	goto activate


:activate_windows
cls
echo           ============================================================
echo                       Activating Windows 10 Pro License...
echo           ============================================================
	slmgr /ipk R84N4-RPC7Q-W8TKM-VM7Y4-7H66Y
	slmgr /ato
	echo.
	echo Windows 10 Pro has been activated successfully!
	if "!runall!"=="0" pause
	if "!runall!"=="0" goto menu
	exit /b

:activate_office
cls
echo           ============================================================
echo                    Activating Office 2019 Pro Plus License...
echo           ============================================================
echo.
	:: Thêm Product Key
	cscript "C:\Program Files\Microsoft Office\Office16\ospp.vbs" /inpkey:Q2NKY-J42YJ-X2KVK-9Q9PT-MKP63
	:: Kích hoạt Office
	cscript "C:\Program Files\Microsoft Office\Office16\ospp.vbs" /act
	echo Office 2019 Pro Plus has been activated successfully!
	if "!runall!"=="0" goto menu
	if "!runall!"=="0" pause
	exit /b

:changeVersion
cls
echo           ============================================================
echo                       Change Version Win10Home to Win10Pro
echo           ============================================================
echo.
	:: Cấu hình dịch vụ LicenseManager và Windows Update
	sc config LicenseManager start= auto & net start LicenseManager
	sc config wuauserv start= auto & net start wuauserv

	:: Thay đổi product key sang Windows 10 Pro
	changepk /productkey VK7JG-NPHTM-C97JM-9MPGT-3V66T

	echo.
	echo Windows upgrade process has been initiated.
	echo Please check your activation status after restart.
	if "!runall!"=="0" goto menu
	if "!runall!"=="0" pause
	exit /b
	
:fire_wall
cls
echo           ============================================================
echo                                SELECT DEVICE TYPE
echo           ============================================================
echo           [1] Set Time/Timezone and Power Options
echo           [2] Turn on Firewall
echo           ------------------------------------------------------------
echo           [0] Return to Menu
echo           ============================================================
	set /p devicetype=Choose your device type [1-2-0]:
	if "%devicetype%"=="1" (
		goto settimezone
	)
	if "%devicetype%"=="2" (
		goto firewall_on
	)
	if "%devicetype%"=="0" (
		goto menu
	)
	echo Invalid choice, please try again!
	pause
	goto install_all

:settimezone
cls
echo           ============================================================
echo                   Setting Time and Timezone and Power Options
echo           ============================================================
echo.
	echo Setting time zone and automatically updating time...
	tzutil /s "SE Asia Standard Time"
	reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\w32time\Parameters" /v Type /t REG_SZ /d NTP /f >nul
	w32tm /resync >nul
	reg add "HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\tzautoupdate" /v Start /t REG_DWORD /d 2 /f >nul
	echo Set timezone and time automatically successfully!
	call :poweroptions
	if "!runall!"=="0" pause
	if "!runall!"=="0" goto menu
	exit /b

:poweroptions
:: Setting Power Options to "Do Nothing"...
	echo Setting Power Options to "Do Nothing"...
	powercfg /SETACVALUEINDEX SCHEME_CURRENT SUB_BUTTONS LIDACTION 0 >nul
	powercfg /SETDCVALUEINDEX SCHEME_CURRENT SUB_BUTTONS LIDACTION 0 >nul
	powercfg /SETACVALUEINDEX SCHEME_CURRENT SUB_BUTTONS SBUTTONACTION 0 >nul
	powercfg /SETDCVALUEINDEX SCHEME_CURRENT SUB_BUTTONS SBUTTONACTION 0 >nul
	powercfg /SETACVALUEINDEX SCHEME_CURRENT SUB_BUTTONS PBUTTONACTION 0 >nul
	powercfg /SETDCVALUEINDEX SCHEME_CURRENT SUB_BUTTONS PBUTTONACTION 0 >nul
	powercfg /SETACVALUEINDEX SCHEME_CURRENT SUB_VIDEO VIDEOIDLE 0 >nul
	powercfg /SETDCVALUEINDEX SCHEME_CURRENT SUB_VIDEO VIDEOIDLE 0 >nul
	powercfg /SETACVALUEINDEX SCHEME_CURRENT SUB_SLEEP STANDBYIDLE 0 >nul
	powercfg /SETDCVALUEINDEX SCHEME_CURRENT SUB_SLEEP STANDBYIDLE 0 >nul
	powercfg /SETACTIVE SCHEME_CURRENT >nul
	echo Power Options setup completed successfully!
	if "!runall!"=="0" pause
	if "!runall!"=="0" goto menu
	exit /b

:firewall_on
	echo Turning on the firewall...
	netsh advfirewall set allprofiles state on
	if "!runall!"=="0" pause
	if "!runall!"=="0" goto menu
	exit /b

:disk
cls
echo           ============================================================
echo                             CHANGE THE DRIVE LETTER
echo           ============================================================
echo           [1] Change the drive letter
echo           [2] Shrink Volume
echo           [3] Extend Volume
echo           [4] Rename
echo           [0] Return to Menu
echo           ============================================================
	set /p rnchoice=Choose an option [1-0]:
	if "%rnchoice%"=="0" goto menu
	if "%rnchoice%"=="1" goto changeLetter
	if "%rnchoice%"=="2" goto shrink
	if "%rnchoice%"=="3" goto merge
	if "%rnchoice%"=="4" goto rename
	echo Invalid choice, please try again!
	pause
	goto disk

:changeLetter
:: Bước 1: Liệt kê tất cả các ổ đĩa hiện tại
	echo List of available drives:
	REM wmic logicaldisk get name,volumename,freespace
	powershell -command "Get-WmiObject Win32_LogicalDisk | Select-Object @{Name='Name';Expression={$_.DeviceID}}, @{Name='VolumeName';Expression={$_.VolumeName}}, @{Name='Size (GB)';Expression={[math]::round($_.Size/1GB, 0)}}, @{Name='FreeSpace (GB)';Expression={[math]::round($_.FreeSpace/1GB, 0)}} | Format-Table -AutoSize"

:: Bước 2: Lấy thông tin từ người dùng
	set /p OldLetter="Select the drive letter to change (Ex: D): "
	set /p NewLetter="Enter a new letter for the drive (Ex: Z): "

:: Bước 3: Tạo file script cho diskpart
	(
		echo select volume !OldLetter!
		echo assign letter=!NewLetter!
	) > ChangeLetter.txt

:: Bước 4: Chạy lệnh diskpart để đổi ký tự
	diskpart /s ChangeLetter.txt

:: Bước 5: Xóa file tạm
	del ChangeLetter.txt

:: Bước 6: Hiển thị kết quả
	echo Changed drive letter to !OldLetter! to !NewsLetter!.
	if "!runall!"=="0" pause
	if "!runall!"=="0" goto menu
	exit /b

:shrink
cls
:: Liệt kê tất cả các ổ đĩa
echo           ============================================================
echo                             List of Available Drives
echo           ============================================================
	powershell -command "Get-WmiObject Win32_LogicalDisk | Select-Object @{Name='Name';Expression={$_.DeviceID}}, @{Name='VolumeName';Expression={$_.VolumeName}}, @{Name='Size (GB)';Expression={[math]::round($_.Size/1GB, 0)}}, @{Name='FreeSpace (GB)';Expression={[math]::round($_.FreeSpace/1GB, 0)}} | Format-Table -AutoSize"
echo           ============================================================

:: Chọn ổ đĩa để thao tác
	set /p selected_drive="Enter the drive letter to partition (e.g., C, D, E): "

:: Kiểm tra ổ đĩa tồn tại
	powershell -command "(Get-WmiObject Win32_LogicalDisk).DeviceID -contains '%selected_drive%:\'" >nul 2>&1
	if errorlevel 1 (
		echo Drive %selected_drive%:\ does not exist. Exiting...
		pause
		exit /b
	)
:: Gợi ý chia dung lượng
	echo.
	echo Choose a partition option:
	echo 1. Create a new partition of 80GB  (for 256GB drives)
	echo 2. Create a new partition of 200GB (for 500GB drives)
	echo 3. Create a new partition of 500GB (for 1TB+ drives)
	set /p partition_option="Enter your choice (1/2/3): "

:: Xử lý lựa chọn
	set size_mb=0
	if "%partition_option%"=="1" (
		set size_mb=82020
		echo You selected 80GB partition.
	) else if "%partition_option%"=="2" (
		set size_mb=204955
		echo You selected 200GB partition.
	) else if "%partition_option%"=="3" (
		set size_mb=512000
		echo You selected 500GB partition.
	) else (
		echo Invalid choice. Exiting...
		pause
		exit /b
	)
	echo Creating partition of size %size_mb% MB on drive %selected_drive%:\...
	(
		echo select volume %selected_drive%
		echo shrink desired=%size_mb%
		echo create partition primary
		echo format fs=ntfs quick
		echo assign
	) > diskpart_script.txt
	diskpart /s diskpart_script.txt
	del diskpart_script.txt

:: Nhập ký tự ổ đĩa
	cls
echo           ============================================================
echo                             List of Available Drives
echo           ============================================================
	powershell -command "Get-WmiObject Win32_LogicalDisk | Select-Object @{Name='Name';Expression={$_.DeviceID}}, @{Name='VolumeName';Expression={$_.VolumeName}}, @{Name='Size (GB)';Expression={[math]::round($_.Size/1GB, 0)}}, @{Name='FreeSpace (GB)';Expression={[math]::round($_.FreeSpace/1GB, 0)}} | Format-Table -AutoSize"
echo           ============================================================
echo.
	set /p drive_letter="Enter the drive letter to rename (e.g., D): "

:: Kiểm tra ký tự ổ đĩa có hợp lệ không
	echo.
	if "%drive_letter%"=="" (
		echo Error: No drive letter entered. Exiting.
		exit /b
	)

:: Nhập tên mới
	set /p new_label="Enter the new label for the partition: "

:: Đổi nhãn phân vùng
	label %drive_letter%: %new_label%

:: Kiểm tra kết quả
	if errorlevel 1 (
		echo Error: Failed to rename the partition.
	) else (
		echo Partition %drive_letter%: successfully renamed to "%new_label%".
	) 
	echo Partition created successfully with name "%new_label%"!
	if "!runall!"=="0" pause
	if "!runall!"=="0" goto menu
	exit /b

:merge
cls
:: Liệt kê ổ đĩa hiện tại
echo           ============================================================
echo                             List of Available Drives
echo           ============================================================
	powershell -command "Get-WmiObject Win32_LogicalDisk | Select-Object @{Name='Name';Expression={$_.DeviceID}}, @{Name='VolumeName';Expression={$_.VolumeName}}, @{Name='Size (GB)';Expression={[math]::round($_.Size/1GB, 0)}}, @{Name='FreeSpace (GB)';Expression={[math]::round($_.FreeSpace/1GB, 0)}} | Format-Table -AutoSize"
echo           ============================================================
echo.

	:: Chọn phân vùng để Merge
	set /p source_drive="Enter the source drive letter to delete (e.g., D): "
	set /p target_drive="Enter the target drive letter to expand (e.g., C): "

	:: Kiểm tra phân vùng nguồn và đích tồn tại
	powershell -command "(Get-WmiObject Win32_Volume).DriveLetter -contains '%source_drive%:\'" >nul 2>&1
	if errorlevel 1 (
		echo ERROR: Source drive %source_drive% does not exist. Exiting...
		pause
		exit /b
	)

	powershell -command "(Get-WmiObject Win32_Volume).DriveLetter -contains '%target_drive%:\'" >nul 2>&1
	if errorlevel 1 (
		echo ERROR: Target drive %target_drive% does not exist. Exiting...
		pause
		exit /b
	)

	:: Xóa phân vùng nguồn
	echo Deleting source drive %source_drive%...
	(
		echo select volume %source_drive%
		echo delete volume
	) > diskpart_script.txt
	diskpart /s diskpart_script.txt
	del diskpart_script.txt

	:: Mở rộng phân vùng đích
	echo Extending target drive %target_drive%...
	(
		echo select volume %target_drive%
		echo extend
	) > diskpart_script.txt
	diskpart /s diskpart_script.txt
	del diskpart_script.txt
	echo Merge completed successfully!
	if "!runall!"=="0" pause
	if "!runall!"=="0" goto menu
	exit /b

:rename
	:: Hiển thị danh sách ổ đĩa
	powershell -command "Get-WmiObject Win32_LogicalDisk | Select-Object DeviceID,VolumeName | Format-Table -AutoSize"
	echo.
	:: enter_drive_letter
	set /p drive_letter="Enter the drive letter to rename (e.g., D): "
	if "%drive_letter%"=="" (
		echo Error: No drive letter entered. Please try again.
		pause
		goto enter_drive_letter
	)

	:: Kiểm tra xem ổ đĩa có tồn tại không
	powershell -command "(Get-WmiObject Win32_LogicalDisk).DeviceID -contains '%drive_letter%:\'" >nul 2>&1
	if errorlevel 1 (
		echo Error: Drive %drive_letter%:\ does not exist. Please try again.
		pause
		goto enter_drive_letter
	)

	:: enter_new_label
	set /p new_label="Enter the new label for the drive %drive_letter%: "

	if "%new_label%"=="" (
		echo Error: No label entered. Please try again.
		pause
		goto enter_new_label
	)

	:: Đổi nhãn ổ đĩa
	label %drive_letter%: %new_label%
	if errorlevel 1 (
		echo Error: Failed to rename the drive. Check the drive letter and try again.
	) else (
		echo Drive %drive_letter%: successfully renamed to "%new_label%".
	)

	if "!runall!"=="0" pause
	if "!runall!"=="0" goto menu
	exit /b

:install_all
cls
echo           ============================================================
echo                               SELECT DEVICE TYPE
echo           ============================================================
echo           [1] Desktop
echo           [2] Laptop
echo           ------------------------------------------------------------
echo           [0] Return to Menu
echo           ============================================================
	set /p devicetype=Choose your device type [1-2-0]:
	if "%devicetype%"=="1" (
		goto install_desktop
	)
	if "%devicetype%"=="2" (
		goto install_laptop
	)
	if "%devicetype%"=="0" (
		goto menu
	)
	echo Invalid choice, please try again!
	pause
	goto install_all

:copy
:: Tạo thư mục tạm trên ổ C
	set TEMP_DIR="%USERPROFILE%\Downloads\SETUP"
	if not exist "%TEMP_DIR%" (
		mkdir "%TEMP_DIR%"
		echo Temporary folder created successfully!
	) else (
		echo Temporary folder already exists.    Skipping...
	)

:: Kiểm tra và copy file từ thư mục PAYOO
	if not exist "%TEMP_DIR%\Software" (
		xcopy "D:\SOFTWARE\PAYOO\SETUP" "%TEMP_DIR%\Software" /E /I /Y
		echo SetupFiles    has been copied successfully!
	) else (
		echo SetupFiles    is already copied.    Skipping...
	)

:: Kiểm tra và copy file Office 2019
	if not exist "%TEMP_DIR%\Office2019" (
		xcopy "D:\SOFTWARE\OFFICE\Office 2019\*" "%TEMP_DIR%\Office2019" /E /I /Y
		echo Office 2019   has been copied successfully!
	) else (
		echo Office 2019   is already copied.    Skipping...
	)

:: Copy thư mục Unikey sang ổ C
	if not exist "C:\unikey46RC2-230919-win64" (
		xcopy "D:\SOFTWARE\PAYOO\unikey46RC2-230919-win64" "C:\unikey46RC2-230919-win64" /E /H /C /I /Y
		echo Unikey        has been copied successfully!
	) else (
		echo Unikey        is already copied.    Skipping...
	)
:: Copy thư mục MSTeamsSetup sang ổ C
	if not exist "C:\MSTeamsSetup.exe" (
		xcopy "D:\SOFTWARE\PAYOO\MSTeamsSetup.exe" "C:\MSTeamsSetup.exe" /E /H /C /I /Y
		echo MSTeamsSetup  has been copied successfully!
	) else (
		echo MSTeamsSetup  is already copied.    Skipping...
	)
:: Copy file ForceScout sang thư mục Downloads
	if not exist "%USERPROFILE%\Downloads\SC-wKgXWicTb0XhUSNethaFN0vkhji53AY5mektJ7O_RSOdc8bEUVIEAAH_OewU.exe" (
		copy /Y "D:\SOFTWARE\PAYOO\SC-wKgXWicTb0XhUSNethaFN0vkhji53AY5mektJ7O_RSOdc8bEUVIEAAH_OewU.exe" "%USERPROFILE%\Downloads"
		echo ForceScout    has been copied successfully!
	) else (
		echo ForceScout    is already copied.    Skipping...
	)
:: Copy file FalconSensor_Windows_installer (All AV).exe sang thư mục Downloads
	if not exist "%USERPROFILE%\Downloads\FalconSensor_Windows_installer (All AV)" (
		copy /Y "D:\SOFTWARE\PAYOO\FalconSensor_Windows_installer (All AV).exe" "%USERPROFILE%\Downloads"
		echo FalconSensor  has been copied successfully!
	) else (
		echo FalconSensor  is already copied.    Skipping...
	)

:install
:: Gỡ cài đặt Microsoft OneDrive nếu đã cài
	if exist "%UserProfile%\OneDrive" (
		echo Uninstalling Microsoft OneDrive...
		"%SystemRoot%\System32\OneDriveSetup.exe" /uninstall
		echo OneDrive uninstalled successfully!
	) else (
		echo OneDrive is not installed or already removed. Skipping...
	)
:: 1.  Cài đặt 7-Zip done
	if not exist "%ProgramFiles%\7-Zip\7zFM.exe" (
		echo Installing 7-Zip...
		start /wait %USERPROFILE%\Downloads\SETUP\Software\7z2408-x64.exe /S
		echo 7-Zip         installed succesfully!
	) else (
		echo 7-Zip         is already installed. Skipping...
	)
:: 2. Cài đặt Google Chrome done
	if not exist "%ProgramFiles%\Google\Chrome\Application\chrome.exe" (
		echo Installing Google Chrome...
		start /wait %USERPROFILE%\Downloads\SETUP\Software\ChromeSetup.exe /silent /install
		echo Google Chrome installed succesfully!
	) else (
		echo Google Chrome is already installed. Skipping...
	)
:: 3. Cài đặt LAPS_x64 done
	if not exist "%ProgramFiles%\LAPS\CSE\AdmPwd.dll" (
		echo Installing LAPS_x64...
		start /wait %USERPROFILE%\Downloads\SETUP\Software\LAPS_x64.msi /quiet
		echo LAPS_x64      installed succesfully!
	) else (
		echo LAPS_x64      is already installed. Skipping...
	)

:: 5. Cài đặt Foxit Reader
	if not exist "%ProgramFiles(x86)%\Foxit Software\Foxit PDF Reader\FoxitPDFReader.exe" (
		echo Installing Foxit Reader...
		start /wait %USERPROFILE%\Downloads\SETUP\Software\FoxitPDFReader20243_enu_Setup_Prom.exe /silent /install
		echo Foxit Reader  installed succesfully!
	) else (
		echo Foxit Reader  is already installed. Skipping...
	)
:: 6. Cài đặt Microsoft Office 2019 (64-bit)
	if not exist "%ProgramFiles%\Microsoft Office\root\Office16\WINWORD.EXE" (
		echo Installing Microsoft Office 2019...
		cd /d "%TEMP_DIR%\Office2019"
		setup.exe /configure configuration.xml
		cd /d "%~dp0"
		echo MSOffice 2019 installed succesfully!
	) else (
		echo MSOffice 2019 is already installed. Skipping...
	)
	exit /b

:install_desktop
cls
echo           ============================================================
echo                           Installing All Software...
echo           ============================================================
echo.
:: Copy file Desktop Agent.exe sang thư mục Downloads
	call :copy
	if not exist "%USERPROFILE%\Downloads\Desktop Agent.exe" (
		copy /Y "D:\SOFTWARE\PAYOO\Desktop Agent.exe" "%USERPROFILE%\Downloads"
		echo Desktop Agent has been copied successfully!
	) else (
		echo Desktop Agent is already copied.    Skipping...
	)
	call :install
	echo All installations completed successfully!
	if "!runall!"=="0" pause
	if "!runall!"=="0" goto menu
	exit /b

:install_laptop
cls
echo           ============================================================
echo                          Installing All Software...
echo           ============================================================
echo.
	call :copy
	:: Copy file Laptop Agent.exe sang thư mục Downloads
	if not exist "%USERPROFILE%\Downloads\Laptop Agent.exe" (
		copy /Y "D:\SOFTWARE\PAYOO\Laptop Agent.exe" "%USERPROFILE%\Downloads"
		echo Laptop Agent  has been copied successfully!!!
	) else (
		echo Laptop Agent  is already copied.    Skipping...
	)
	:: Copy thư mục ManageEngine_MDMLaptopEnrollment sang thư mục Downloads
	if not exist "%USERPROFILE%\Downloads\ManageEngine_MDMLaptopEnrollment" (
		xcopy /E /I /Y "D:\SOFTWARE\PAYOO\ManageEngine_MDMLaptopEnrollment" "%USERPROFILE%\Downloads\ManageEngine_MDMLaptopEnrollment"
		echo MDM           has been copied successfully!
	) else (
		echo MDM           is already copied.    Skipping...
	)
	call :install
:: 7.Cài đặt Zoom done
	if not exist "%USERPROFILE%\AppData\Roaming\Zoom\bin\Zoom.exe" (
		echo Installing Zoom...
		start /wait %USERPROFILE%\Downloads\SETUP\Software\ZoomInstallerFull.exe /silent /install
		echo Zoom installed succesfully!!!
	) else (
		echo Zoom          is already installed. Skipping...
	)
:: 8.Cài đặt CheckPointVPN done
	if not exist "%ProgramFiles(x86)%\CheckPoint\Endpoint Connect\TrGUI.exe" (
		echo Installing CheckPointVPN...
		start /wait %USERPROFILE%\Downloads\SETUP\Software\CheckPointVPN.msi /quiet
		echo CheckPointVPN installed succesfully!
	) else (
		echo CheckPointVPN is already installed. Skipping...
	)
	echo All installations completed successfully!
	if "!runall!"=="0" pause
	if "!runall!"=="0" goto menu
	exit /b

:turnon_features
cls
echo           ============================================================
echo                     Enabling Windows Features and Disable IE11
echo           ============================================================
	:: Kiểm tra và bật .NET Framework 3.5
	for /f "tokens=2 delims=:" %%a in ('dism /online /get-featureinfo /featurename:NetFx3 ^| find "State"') do (
		if "%%a"==" Disabled" (
			echo Enabling .NET Framework 3.5...
			dism /online /enable-feature /featurename:NetFx3 /all /norestart
		) else (
			echo .NET Framework 3.5     is already enabled.
		)
	)

	:: Kiểm tra và bật WCF-HTTP-Activation
	for /f "tokens=2 delims=:" %%a in ('dism /online /get-featureinfo /featurename:WCF-HTTP-Activation ^| find "State"') do (
		if "%%a"==" Disabled" (
			echo Enabling WCF-HTTP-Activation...
			DISM /Online /Enable-Feature /FeatureName:WCF-HTTP-Activation /All /Quiet /NoRestart
		) else (
			echo WCF-HTTP-Activation    is already enabled.
		)
	)
	:: Kiểm tra và bật WCF-NonHTTP-Activation
	for /f "tokens=2 delims=:" %%a in ('dism /online /get-featureinfo /featurename:WCF-NonHTTP-Activation ^| find "State"') do (
		if "%%a"==" Disabled" (
			echo Enabling WCF-NonHTTP-Activation...
			DISM /Online /Enable-Feature /FeatureName:WCF-NonHTTP-Activation /All /Quiet /NoRestart
		) else (
			echo WCF-NonHTTP-Activation is already enabled.
		)
	)

	:: Kiểm tra và tắt Internet Explorer 11
	for /f "tokens=2 delims=:" %%a in ('dism /online /get-featureinfo /featurename:Internet-Explorer-Optional-amd64 ^| find "State"') do (
		if "%%a"==" Enabled" (
			echo Disabling Internet Explorer 11...
			dism /online /disable-feature /featurename:Internet-Explorer-Optional-amd64 /norestart
		) else (
			echo Internet Explorer 11   is already disabled.
		)
	)
	echo.
	echo Windows Features updated successfully!
	if "!runall!"=="0" pause
	if "!runall!"=="0" goto menu
	exit /b

:renamedevice
cls
echo           ============================================================
echo                              RENAME DEVICE OPTION
echo           ============================================================
echo           [1] Rename Device
echo           [0] Return to Main Menu
echo           ============================================================
	set /p rnchoice=Choose an option [1-0]:
	if "%rnchoice%"=="0" goto menu
	cls
	set /p NewName=Enter the new Device name:
	wmic computersystem where name="%COMPUTERNAME%" call rename name="%NewName%"
	echo The Device name will be changed after reboot!
	if "!runall!"=="0" pause
	if "!runall!"=="0" goto menu
	exit /b

:setpassword
cls
echo           ============================================================
echo                           SET PASSWORD FOR THIS USER
echo           ============================================================
echo           [1] Set Password
echo           [0] Return to Main Menu
echo           ============================================================
	set /p rnchoice=Choose an option [1-0]:
	if "%rnchoice%"=="0" goto menu
	cls
:: Lấy tên user hiện tại
	for /f "tokens=2 delims=," %%i in ('query user %username%') do set current_user=%%i
	echo The current user is: %username%

:: Đặt mật khẩu mới
	set /p new_password=Enter the new password:

	net user "%username%" "%new_password%"
	if %errorlevel%==0 (
		echo Password changed successfully!
	) else (
		echo Failed to change the password. Please check your permissions.
	)

	if "!runall!"=="0" pause
	if "!runall!"=="0" goto menu
	exit /b

:join_domain
cls
echo           ============================================================
echo                                  Join Domain
echo           ============================================================
echo           [1] Join Domain
echo           [0] Return to Main Menu
echo           ============================================================
	set /p rnchoice=Choose an option [1-0]:
	if "%rnchoice%"=="0" goto menu
	cls
	echo Opening the Change Computer Name/Domain window...
	:: Mở bảng "System Properties"
	start SystemPropertiesComputerName
	goto menu

:startup
cls
echo           ============================================================
echo                          ENABLE/DISABLE STARTUP PROGRAMS
echo           ============================================================
echo           [1] Enable a Startup Program
echo           [2] Disable a Startup Program
echo           ------------------------------------------------------------
echo           [0] Return to Menu
echo           ============================================================
set /p startup_choice=Choose an option [1-3-0]:

if "%startup_choice%"=="1" goto enable_startup
if "%startup_choice%"=="2" goto disable_startup
if "%startup_choice%"=="0" goto menu

echo Invalid choice, please try again!
pause
goto startup

:enable_startup
cls
echo           ============================================================
echo                          ENABLE A STARTUP PROGRAM
echo           ============================================================
echo Enter the name of the program to enable (e.g., OneDrive):
set /p program_name=Program Name: 
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "%program_name%" /t REG_SZ /d "Path_to_Program" /f
echo Program "%program_name%" has been enabled to start at login!
pause
goto startup

:disable_startup
cls
echo           ============================================================
echo                          DISABLE OF STARTUP PROGRAMS
echo           ============================================================
reg query "HKCU\Software\Microsoft\Windows\CurrentVersion\Run"
echo Enter the name of the program to disable (e.g., OneDrive):
set /p program_name=Program Name: 
reg delete "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "%program_name%" /f
echo Program "%program_name%" has been disabled from starting at login!
pause
goto startup

:exit
cls
:: Xóa thư mục tạm
rd /s /q "%TEMP_DIR%"
cls
echo.
echo.
echo.
echo.
echo.
echo.
echo.
echo.
echo.
echo.
echo           ============================================================
echo                         Thank you for using this script!
echo           ============================================================
timeout /t 3 >nul
exit