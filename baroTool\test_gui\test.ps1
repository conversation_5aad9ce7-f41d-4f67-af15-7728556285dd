# # ADMIN PRIVILEGES CHECK & INITIALIZATION
# if (-NOT ([Security.Principal.WindowsPrincipal][Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
#     Write-Warning "This script requires administrative privileges. Attempting to restart with elevation..."

#     # Restart script with admin privileges
#     $scriptPath = $MyInvocation.MyCommand.Path
#     $arguments = "-ExecutionPolicy Bypass -WindowStyle Hidden -File `"$scriptPath`""

#     Start-Process powershell.exe -ArgumentList $arguments -Verb RunAs

#     # Exit the current non-elevated instance
#     exit
# }

# ẨN CONSOLE
try {
    Add-Type -Name Window -Namespace Console -MemberDefinition '
    [DllImport("Kernel32.dll")]
    public static extern IntPtr GetConsoleWindow();
    [DllImport("user32.dll")]
    public static extern bool ShowWindow(IntPtr hWnd, Int32 nCmdShow);
    ' -ErrorAction SilentlyContinue
    
    $consolePtr = [Console.Window]::GetConsoleWindow()
    if ($consolePtr -ne [System.IntPtr]::Zero) {
        [Console.Window]::ShowWindow($consolePtr, 0)
    }
} catch {
}

# Ẩn menu chính
function Hide-MainMenu {
    $script:form.Hide()
}

# Hiện menu chính
function Show-MainMenu {
    $script:form.Show()
}

# Tạo nút động
function New-DynamicButton {
    param (
        [string]$text,
        [int]$x,
        [int]$y,
        [int]$width,
        [int]$height,
        [scriptblock]$clickAction,
        [System.Drawing.Color]$normalColor = [System.Drawing.Color]::FromArgb(0, 128, 0),
        [System.Drawing.Color]$hoverColor = [System.Drawing.Color]::FromArgb(0, 180, 0),
        [System.Drawing.Color]$pressColor = [System.Drawing.Color]::FromArgb(0, 100, 0),
        [System.Drawing.Color]$textColor = [System.Drawing.Color]::White,
        [string]$fontName = "Arial",
        [int]$fontSize = 12,
        [System.Drawing.FontStyle]$fontStyle = [System.Drawing.FontStyle]::Bold
    )

    $button = New-Object System.Windows.Forms.Button
    $button.Text = $text
    $button.Location = New-Object System.Drawing.Point($x, $y)
    $button.Size = New-Object System.Drawing.Size($width, $height)
    $button.FlatStyle = [System.Windows.Forms.FlatStyle]::Flat
    $button.BackColor = $normalColor
    $button.ForeColor = $textColor
    $button.Font = New-Object System.Drawing.Font($fontName, $fontSize, $fontStyle)
    $button.FlatAppearance.BorderSize = 0
    $button.FlatAppearance.MouseOverBackColor = $hoverColor
    $button.FlatAppearance.MouseDownBackColor = $pressColor
    $button.Cursor = [System.Windows.Forms.Cursors]::Hand
    $button.Add_Click($clickAction)

    return $button
}

# Lệnh kiểm tra và tải thư viện Windows Forms
Add-Type -AssemblyName System.Windows.Forms -ErrorAction Stop
Add-Type -AssemblyName System.Drawing -ErrorAction Stop

# Tạo form chính có thể thay đổi kích thước
$script:form = New-Object System.Windows.Forms.Form
$script:form.Text = "BAOPROVIP - SYSTEM MANAGEMENT"
$script:form.Size = New-Object System.Drawing.Size(600, 400)
$script:form.MinimumSize = New-Object System.Drawing.Size(600, 400)  # Kích thước tối thiểu
$script:form.StartPosition = "CenterScreen"
$script:form.BackColor = [System.Drawing.Color]::Black
$script:form.FormBorderStyle = [System.Windows.Forms.FormBorderStyle]::Sizable  # CHỖ NÀY THAY ĐỔI
$script:form.MaximizeBox = $true  # Cho phép maximize

# Thêm màu gradient với resize handling
$script:form.Add_Paint({
    $graphics = $_.Graphics
    $rect = New-Object System.Drawing.Rectangle(0, 0, $script:form.Width, $script:form.Height)
    $brush = New-Object System.Drawing.Drawing2D.LinearGradientBrush(
        $rect,
        [System.Drawing.Color]::FromArgb(0, 0, 0),
        [System.Drawing.Color]::FromArgb(0, 30, 0),
        [System.Drawing.Drawing2D.LinearGradientMode]::Vertical
    )
    $graphics.FillRectangle($brush, $rect)
    $brush.Dispose()
})

# Tiêu đề - RESPONSIVE
$titleLabel = New-Object System.Windows.Forms.Label
$titleLabel.Text = "WELCOME TO BAOPROVIP"
$titleLabel.Font = New-Object System.Drawing.Font("Arial", 20, [System.Drawing.FontStyle]::Bold)
$titleLabel.ForeColor = [System.Drawing.Color]::FromArgb(0, 255, 0)
$titleLabel.TextAlign = [System.Drawing.ContentAlignment]::MiddleCenter
$titleLabel.Size = New-Object System.Drawing.Size($script:form.ClientSize.Width, 60)
$titleLabel.Location = New-Object System.Drawing.Point(0, 20)
$titleLabel.BackColor = [System.Drawing.Color]::Transparent
$titleLabel.Anchor = [System.Windows.Forms.AnchorStyles]::Top -bor [System.Windows.Forms.AnchorStyles]::Left -bor [System.Windows.Forms.AnchorStyles]::Right
$script:form.Controls.Add($titleLabel)


# Hàm thông báo trạng thái
function Add-Status {
        param(
            [string]$message,
            [System.Windows.Forms.TextBox]$statusTextBox
        )

        if ($statusTextBox.Text -eq "Please select a device type...") {
            $statusTextBox.Clear()
        }

        $timestamp = Get-Date -Format "HH:mm:ss"
        $statusTextBox.AppendText("[$timestamp] $message`r`n")
        $statusTextBox.ScrollToCaret()
        [System.Windows.Forms.Application]::DoEvents()
}

# [7] Rename Device Functions
    function Rename-DeviceWithBatch {
    param(
        [Parameter(Mandatory = $true)]
        [string]$newName,
        
        [scriptblock]$statusCallback,
        
        [bool]$showUI = $false
    )
    
    # Function to add status
    function Add-Status {
        param([string]$message)
        if ($statusCallback) {
            & $statusCallback $message
        }
    }
    
    try {
        # Validate computer name
        if ([string]::IsNullOrWhiteSpace($newName)) {
            $errorMsg = "Error: New computer name cannot be empty."
            Add-Status $errorMsg
            if ($showUI) {
                [System.Windows.Forms.MessageBox]::Show($errorMsg, "Validation Error", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Error)
            }
            return $false
        }
        
        # Remove spaces and convert to uppercase
        $newName = $newName.Trim().ToUpper()
        
        # Validate computer name format
        if ($newName.Length -gt 15) {
            $errorMsg = "Error: Computer name cannot exceed 15 characters."
            Add-Status $errorMsg
            if ($showUI) {
                [System.Windows.Forms.MessageBox]::Show($errorMsg, "Validation Error", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Error)
            }
            return $false
        }
        
        if ($newName -match '[^A-Z0-9-]') {
            $errorMsg = "Error: Computer name can only contain letters, numbers, and hyphens."
            Add-Status $errorMsg
            if ($showUI) {
                [System.Windows.Forms.MessageBox]::Show($errorMsg, "Validation Error", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Error)
            }
            return $false
        }
        
        # Check if name is the same as current
        $currentName = $env:COMPUTERNAME
        if ($newName -eq $currentName) {
            $errorMsg = "Error: New name is the same as current name ($currentName)."
            Add-Status $errorMsg
            if ($showUI) {
                [System.Windows.Forms.MessageBox]::Show($errorMsg, "Validation Error", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Error)
            }
            return $false
        }
        
        Add-Status "Validating new computer name: $newName"
        Add-Status "Current computer name: $currentName"
        
        # Confirm with user if showUI is enabled
        if ($showUI) {
            $confirmResult = [System.Windows.Forms.MessageBox]::Show(
                "Are you sure you want to rename this computer from '$currentName' to '$newName'?`n`nThis will require a restart to take effect.",
                "Confirm Computer Rename",
                [System.Windows.Forms.MessageBoxButtons]::YesNo,
                [System.Windows.Forms.MessageBoxIcon]::Question
            )
            
            if ($confirmResult -ne [System.Windows.Forms.DialogResult]::Yes) {
                Add-Status "Operation cancelled by user."
                return $false
            }
        }
        
        # Create batch file for renaming computer
        $batchFilePath = [System.IO.Path]::GetTempFileName() + ".bat"
        $batchContent = @"
@echo off
echo ============================================================ > rename_log.txt
echo              Computer Rename Operation >> rename_log.txt
echo ============================================================ >> rename_log.txt
echo. >> rename_log.txt
echo Current name: $currentName >> rename_log.txt
echo New name: $newName >> rename_log.txt
echo. >> rename_log.txt

echo Renaming computer to $newName... >> rename_log.txt
powershell -WindowStyle Hidden -Command "& { try { Rename-Computer -NewName '$newName' -Force -ErrorAction Stop; Write-Output 'Computer renamed successfully.' } catch { Write-Error `$_.Exception.Message; exit 1 } }" > rename_output.txt 2>&1

type rename_output.txt >> rename_log.txt

if errorlevel 1 (
    echo PowerShell rename failed, trying wmic... >> rename_log.txt
    wmic computersystem where name="%COMPUTERNAME%" call rename name="$newName" > wmic_output.txt 2>&1
    type wmic_output.txt >> rename_log.txt
    
    if errorlevel 1 (
        echo ERROR: Failed to rename computer using both PowerShell and WMIC. >> rename_log.txt
        del wmic_output.txt
        del rename_output.txt
        exit /b 1
    )
    del wmic_output.txt
) else (
    echo Successfully renamed computer using PowerShell. >> rename_log.txt
)
del rename_output.txt

echo. >> rename_log.txt
echo Computer rename completed successfully! >> rename_log.txt
echo A restart is required for the changes to take effect. >> rename_log.txt
exit /b 0
"@
        
        Set-Content -Path $batchFilePath -Value $batchContent -Force -Encoding ASCII
        
        Add-Status "Renaming computer from '$currentName' to '$newName'..."
        Add-Status "Processing... Please wait while the operation completes."
        
        # Create a process to run batch file with admin privileges
        $psi = New-Object System.Diagnostics.ProcessStartInfo
        $psi.FileName = "cmd.exe"
        $psi.Arguments = "/c `"$batchFilePath`""
        $psi.UseShellExecute = $true
        $psi.Verb = "runas"
        $psi.WindowStyle = [System.Diagnostics.ProcessWindowStyle]::Hidden

        # Run process
        $batchProcess = [System.Diagnostics.Process]::Start($psi)
        $batchProcess.WaitForExit()
        
        # Check if operation was successful
        if ($batchProcess.ExitCode -eq 0) {
            Add-Status "Computer successfully renamed to '$newName'."
            Add-Status "A restart is required for the changes to take effect."
            
            if ($showUI) {
                $restartResult = [System.Windows.Forms.MessageBox]::Show(
                    "Computer has been successfully renamed to '$newName'.`n`nA restart is required for the changes to take effect.`n`nWould you like to restart now?",
                    "Rename Successful",
                    [System.Windows.Forms.MessageBoxButtons]::YesNo,
                    [System.Windows.Forms.MessageBoxIcon]::Information
                )
                
                if ($restartResult -eq [System.Windows.Forms.DialogResult]::Yes) {
                    Add-Status "Restarting computer..."
                    Start-Process -FilePath "shutdown.exe" -ArgumentList "/r /t 5" -NoNewWindow
                }
            }
            
            $success = $true
        }
        else {
            Add-Status "Operation completed with warnings or errors."
            Add-Status "Exit code: $($batchProcess.ExitCode)"
            
            if ($showUI) {
                [System.Windows.Forms.MessageBox]::Show(
                    "Computer rename operation completed with warnings.`n`nPlease check if the operation was successful and restart manually if needed.",
                    "Operation Completed",
                    [System.Windows.Forms.MessageBoxButtons]::OK,
                    [System.Windows.Forms.MessageBoxIcon]::Warning
                )
            }
            
            $success = $true # Still consider it successful as it might have worked
        }
        
        # Clean up files
        Remove-Item $batchFilePath -Force -ErrorAction SilentlyContinue
        Remove-Item "rename_log.txt" -Force -ErrorAction SilentlyContinue
        
        return $success
    }
    catch {
        $errorMsg = "Error during computer rename operation: $_"
        Add-Status $errorMsg
        
        if ($showUI) {
            [System.Windows.Forms.MessageBox]::Show(
                $errorMsg + "`n`nMake sure you have administrator privileges.",
                "Error",
                [System.Windows.Forms.MessageBoxButtons]::OK,
                [System.Windows.Forms.MessageBoxIcon]::Error
            )
        }
        
        # Clean up files
        if (Test-Path $batchFilePath) {
            Remove-Item $batchFilePath -Force -ErrorAction SilentlyContinue
        }
        Remove-Item "rename_log.txt" -Force -ErrorAction SilentlyContinue
        
        return $false
    }
    }

    function Invoke-RenameDialog {
        Hide-MainMenu
        # Create device rename form
        $renameForm = New-Object System.Windows.Forms.Form
        $renameForm.Text = "Rename Device"
        $renameForm.Size = New-Object System.Drawing.Size(500, 480) # Increased height for status box
        $renameForm.StartPosition = "CenterScreen"
        $renameForm.BackColor = [System.Drawing.Color]::Black
        $renameForm.FormBorderStyle = [System.Windows.Forms.FormBorderStyle]::FixedDialog
        $renameForm.MaximizeBox = $false
        $renameForm.MinimizeBox = $false

        # Create title label
        $titleLabel = New-Object System.Windows.Forms.Label
        $titleLabel.Text = "Rename Current Device"
        $titleLabel.Font = New-Object System.Drawing.Font("Arial", 14, [System.Drawing.FontStyle]::Bold)
        $titleLabel.ForeColor = [System.Drawing.Color]::FromArgb(0, 255, 0)
        $titleLabel.TextAlign = [System.Drawing.ContentAlignment]::MiddleCenter
        $titleLabel.Size = New-Object System.Drawing.Size(480, 40)
        $titleLabel.Location = New-Object System.Drawing.Point(10, 20)
        $renameForm.Controls.Add($titleLabel)

        # Get current computer name
        $currentName = $env:COMPUTERNAME

        # Current device name label
        $currentLabel = New-Object System.Windows.Forms.Label
        $currentLabel.Text = "Current Device Name: $currentName"
        $currentLabel.Font = New-Object System.Drawing.Font("Arial", 12)
        $currentLabel.ForeColor = [System.Drawing.Color]::White
        $currentLabel.Size = New-Object System.Drawing.Size(480, 30)
        $currentLabel.Location = New-Object System.Drawing.Point(20, 70)
        $renameForm.Controls.Add($currentLabel)

        # Device type selection group box
        $deviceGroupBox = New-Object System.Windows.Forms.GroupBox
        $deviceGroupBox.Text = "Device Type"
        $deviceGroupBox.Font = New-Object System.Drawing.Font("Arial", 10)
        $deviceGroupBox.ForeColor = [System.Drawing.Color]::White
        $deviceGroupBox.Size = New-Object System.Drawing.Size(460, 80)
        $deviceGroupBox.Location = New-Object System.Drawing.Point(20, 110)
        $deviceGroupBox.BackColor = [System.Drawing.Color]::Black

        # Desktop radio button
        $radioDesktop = New-Object System.Windows.Forms.RadioButton
        $radioDesktop.Text = "Desktop (HOD)"
        $radioDesktop.Font = New-Object System.Drawing.Font("Arial", 10)
        $radioDesktop.ForeColor = [System.Drawing.Color]::White
        $radioDesktop.Location = New-Object System.Drawing.Point(20, 30)
        $radioDesktop.Size = New-Object System.Drawing.Size(200, 30)
        $radioDesktop.BackColor = [System.Drawing.Color]::Black
        $radioDesktop.Checked = $true # Default selection

        # Laptop radio button
        $radioLaptop = New-Object System.Windows.Forms.RadioButton
        $radioLaptop.Text = "Laptop (HOL)"
        $radioLaptop.Font = New-Object System.Drawing.Font("Arial", 10)
        $radioLaptop.ForeColor = [System.Drawing.Color]::White
        $radioLaptop.Location = New-Object System.Drawing.Point(240, 30)
        $radioLaptop.Size = New-Object System.Drawing.Size(200, 30)
        $radioLaptop.BackColor = [System.Drawing.Color]::Black

        # Add radio buttons to group box
        $deviceGroupBox.Controls.Add($radioDesktop)
        $deviceGroupBox.Controls.Add($radioLaptop)
        $renameForm.Controls.Add($deviceGroupBox)

        # New name label
        $newNameLabel = New-Object System.Windows.Forms.Label
        $newNameLabel.Text = "New Device Name:"
        $newNameLabel.Font = New-Object System.Drawing.Font("Arial", 12)
        $newNameLabel.ForeColor = [System.Drawing.Color]::White
        $newNameLabel.Size = New-Object System.Drawing.Size(150, 30)
        $newNameLabel.Location = New-Object System.Drawing.Point(20, 200)
        $renameForm.Controls.Add($newNameLabel)

        # New name textbox
        $newNameTextBox = New-Object System.Windows.Forms.TextBox
        $newNameTextBox.Font = New-Object System.Drawing.Font("Arial", 12)
        $newNameTextBox.Size = New-Object System.Drawing.Size(300, 30)
        $newNameTextBox.Location = New-Object System.Drawing.Point(170, 200)
        $newNameTextBox.BackColor = [System.Drawing.Color]::White
        $newNameTextBox.ForeColor = [System.Drawing.Color]::Black
        $newNameTextBox.Text = "HOD" # Default to Desktop
        $renameForm.Controls.Add($newNameTextBox)

        # Event handlers for radio buttons to update the default name
        $radioDesktop.Add_CheckedChanged({
                if ($radioDesktop.Checked) {
                    $newNameTextBox.Text = "HOD"
                }
            })

        $radioLaptop.Add_CheckedChanged({
                if ($radioLaptop.Checked) {
                    $newNameTextBox.Text = "HOL"
                }
            })

        # Status text box
        $statusTextBox = New-Object System.Windows.Forms.TextBox
        $statusTextBox.Multiline = $true
        $statusTextBox.ScrollBars = "Vertical"
        $statusTextBox.Location = New-Object System.Drawing.Point(20, 320)
        $statusTextBox.Size = New-Object System.Drawing.Size(460, 120)
        $statusTextBox.BackColor = [System.Drawing.Color]::Black
        $statusTextBox.ForeColor = [System.Drawing.Color]::Lime
        $statusTextBox.Font = New-Object System.Drawing.Font("Consolas", 9)
        $statusTextBox.ReadOnly = $true
        $statusTextBox.BorderStyle = [System.Windows.Forms.BorderStyle]::FixedSingle
        $statusTextBox.Text = "Ready to rename device..."
        $renameForm.Controls.Add($statusTextBox)

        # Function to add status message
        function Add-Status {
            param([string]$message)
            $statusTextBox.AppendText("`r`n$(Get-Date -Format 'HH:mm:ss') - $message")
            $statusTextBox.ScrollToCaret()
            [System.Windows.Forms.Application]::DoEvents()
        }

        # Rename button
        $renameButton = New-Object System.Windows.Forms.Button
        $renameButton.Text = "Rename Device"
        $renameButton.Font = New-Object System.Drawing.Font("Arial", 12, [System.Drawing.FontStyle]::Bold)
        $renameButton.ForeColor = [System.Drawing.Color]::White
        $renameButton.BackColor = [System.Drawing.Color]::FromArgb(0, 180, 0)
        $renameButton.Size = New-Object System.Drawing.Size(200, 40)
        $renameButton.Location = New-Object System.Drawing.Point(30, 260)
        $renameButton.FlatStyle = [System.Windows.Forms.FlatStyle]::Flat
        $renameButton.Add_Click({
                $newName = $newNameTextBox.Text.Trim()

                # Disable the rename button to prevent multiple clicks
                $renameButton.Enabled = $false

                # Call the rename function with a status callback
                [Diagnostics.CodeAnalysis.SuppressMessageAttribute('PSUseDeclaredVarsMoreThanAssignments', 'result')]
                $result = Rename-DeviceWithBatch -newName $newName -statusCallback ${function:Add-Status} -showUI $true

                # Re-enable the rename button
                $renameButton.Enabled = $true
            })
        $renameForm.Controls.Add($renameButton)

        # Cancel button
        $cancelButton = New-DynamicButton {} -text "Cancel" -x 250 -y 260 -width 200 -height 40 -clickAction  {
            $renameForm.Close()
        }
        $renameForm.Controls.Add($cancelButton)

        # Set the accept button (Enter key)
        $renameForm.AcceptButton = $renameButton
        # Set the cancel button (Escape key)
        $renameForm.CancelButton = $cancelButton

        # When the form is closed, show the main menu again
        $renameForm.Add_FormClosed({
            Show-MainMenu
        })

        # Show the form
        $renameForm.ShowDialog()
    }

# [8] Password Functions
    function Show-SetPasswordForm {
        param(
            [string]$currentUser
        )

        # Set Password Form
        $form = New-Object System.Windows.Forms.Form
        $form.Text = "Set Password"
        $form.Size = New-Object System.Drawing.Size(500, 270)
        $form.StartPosition = "CenterScreen"
        $form.BackColor = [System.Drawing.Color]::Black
        $form.FormBorderStyle = [System.Windows.Forms.FormBorderStyle]::FixedDialog
        $form.MaximizeBox = $false
        $form.MinimizeBox = $false
    
        # Title
        $titleLabel = New-Object System.Windows.Forms.Label
        $titleLabel.Text = "Set Password for Current User"
        $titleLabel.Font = New-Object System.Drawing.Font("Arial", 14, [System.Drawing.FontStyle]::Bold)
        $titleLabel.ForeColor = [System.Drawing.Color]::FromArgb(0, 255, 0)
        $titleLabel.TextAlign = [System.Drawing.ContentAlignment]::MiddleCenter
        $titleLabel.Size = New-Object System.Drawing.Size(480, 40)
        $titleLabel.Location = New-Object System.Drawing.Point(10, 20)
        $form.Controls.Add($titleLabel)
    
        # User label
        $userLabel = New-Object System.Windows.Forms.Label
        $userLabel.Text = "Current User:            $currentUser"
        $userLabel.Font = New-Object System.Drawing.Font("Arial", 12)
        $userLabel.ForeColor = [System.Drawing.Color]::White
        $userLabel.Size = New-Object System.Drawing.Size(480, 30)
        $userLabel.Location = New-Object System.Drawing.Point(30, 70)
        $form.Controls.Add($userLabel)
    
        # Password label
        $passwordLabel = New-Object System.Windows.Forms.Label
        $passwordLabel.Text = "New Password:"
        $passwordLabel.Font = New-Object System.Drawing.Font("Arial", 12)
        $passwordLabel.ForeColor = [System.Drawing.Color]::White
        $passwordLabel.Size = New-Object System.Drawing.Size(130, 30)
        $passwordLabel.Location = New-Object System.Drawing.Point(30, 110)
        $form.Controls.Add($passwordLabel)
    
        # Password textbox
        $passwordTextBox = New-Object System.Windows.Forms.TextBox
        $passwordTextBox.Font = New-Object System.Drawing.Font("Arial", 12)
        $passwordTextBox.Size = New-Object System.Drawing.Size(200, 30)
        $passwordTextBox.Location = New-Object System.Drawing.Point(180, 110)
        $passwordTextBox.BackColor = [System.Drawing.Color]::White
        $passwordTextBox.ForeColor = [System.Drawing.Color]::Black
        $passwordTextBox.UseSystemPasswordChar = $false # Mặc định hiển thị password
        $form.Controls.Add($passwordTextBox)
    
        # Show Password checkbox (default checked)
        $showPasswordCheckBox = New-Object System.Windows.Forms.CheckBox
        $showPasswordCheckBox.Text = "Show"
        $showPasswordCheckBox.Location = New-Object System.Drawing.Point(400, 115)
        $showPasswordCheckBox.Size = New-Object System.Drawing.Size(100, 20)
        $showPasswordCheckBox.ForeColor = [System.Drawing.Color]::White
        $showPasswordCheckBox.Font = New-Object System.Drawing.Font("Arial", 9)
        $showPasswordCheckBox.BackColor = [System.Drawing.Color]::Transparent
        $showPasswordCheckBox.Checked = $true
        $showPasswordCheckBox.Add_CheckedChanged({
            $passwordTextBox.UseSystemPasswordChar = -not $showPasswordCheckBox.Checked
        })
        $form.Controls.Add($showPasswordCheckBox)
    
        # Info label for empty password
        $infoLabel = New-Object System.Windows.Forms.Label
        $infoLabel.Text = "Leave the password field empty to set a blank password."
        $infoLabel.Font = New-Object System.Drawing.Font("Arial", 9, [System.Drawing.FontStyle]::Bold)
        $infoLabel.ForeColor = [System.Drawing.Color]::Red
        $infoLabel.Size = New-Object System.Drawing.Size(450, 20)
        $infoLabel.Location = New-Object System.Drawing.Point(70, 145)
        $form.Controls.Add($infoLabel)
    
        # Set Password button
        $setButton = New-Object System.Windows.Forms.Button
        $setButton.Text = "Set Password"
        $setButton.Font = New-Object System.Drawing.Font("Arial", 12, [System.Drawing.FontStyle]::Bold)
        $setButton.ForeColor = [System.Drawing.Color]::White
        $setButton.BackColor = [System.Drawing.Color]::FromArgb(0, 180, 0)
        $setButton.Size = New-Object System.Drawing.Size(200, 40)
        $setButton.Location = New-Object System.Drawing.Point(30, 180)
        $setButton.FlatStyle = [System.Windows.Forms.FlatStyle]::Flat
        $setButton.Add_Click({
            $password = $passwordTextBox.Text
            try {
                # Create a command to set the password
                if ([string]::IsNullOrEmpty($password)) {
                    $command = "net user $currentUser """""
                } else {
                    $command = "net user $currentUser $password"
                }
                $process = Start-Process -FilePath "cmd.exe" -ArgumentList "/c $command" -NoNewWindow -Wait -PassThru
                if ($process.ExitCode -eq 0) {
                    if ([string]::IsNullOrEmpty($password)) {
                        [System.Windows.Forms.MessageBox]::Show("Password has been removed. User '$currentUser' can now log in without a password.", "Password Removed", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Information)
                    } else {
                        [System.Windows.Forms.MessageBox]::Show("Password has been changed.", "Password Change", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Information)
                    }
                    $form.Close()
                } else {
                    throw "Failed to set password. Exit code: $($process.ExitCode)"
                }
            }
            catch {
                [System.Windows.Forms.MessageBox]::Show("Error setting password: $_`n`nNote: This operation requires administrative privileges.", "Error", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Error)
            }
        })
        $form.Controls.Add($setButton)
    
        # Cancel button
        $cancelButton = New-Object System.Windows.Forms.Button
        $cancelButton.Text = "Cancel"
        $cancelButton.Font = New-Object System.Drawing.Font("Arial", 12, [System.Drawing.FontStyle]::Bold)
        $cancelButton.ForeColor = [System.Drawing.Color]::White
        $cancelButton.BackColor = [System.Drawing.Color]::FromArgb(150, 0, 0)
        $cancelButton.Size = New-Object System.Drawing.Size(200, 40)
        $cancelButton.Location = New-Object System.Drawing.Point(250, 180)
        $cancelButton.FlatStyle = [System.Windows.Forms.FlatStyle]::Flat
        $cancelButton.Add_Click({
            $form.Close()
        })
        $form.Controls.Add($cancelButton)
    
        # Set Accept/Cancel button for Enter/Esc
        $form.AcceptButton = $setButton
        $form.CancelButton = $cancelButton
    
        # Focus on password textbox when form shows
        $form.Add_Shown({
            $passwordTextBox.Focus()
        })
    
        # Show the form
        $form.ShowDialog()
    } 

    function Set-UserPassword {
    param(
        [string]$user,
        [string]$password
    )
    try {
        if ([string]::IsNullOrEmpty($password)) {
            # Xóa mật khẩu (blank)
            $command = "net user $user """""
        } else {
            $command = "net user $user $password"
        }
        $process = Start-Process -FilePath "cmd.exe" -ArgumentList "/c $command" -NoNewWindow -Wait -PassThru
        return $process.ExitCode -eq 0
    } catch {
        return $false
    }
    }

    function Remove-UserPassword {
    param(
        [string]$user
    )
    try {
        $command = "net user $user """""
        $process = Start-Process -FilePath "cmd.exe" -ArgumentList "/c $command" -NoNewWindow -Wait -PassThru
        return $process.ExitCode -eq 0
    } catch {
        return $false
    }
    }

    function Invoke-SetPasswordDialog {
    $currentUser = $env:USERNAME
    Hide-MainMenu
    $result = Show-SetPasswordForm -currentUser $currentUser

    if ($result.Action -eq "set") {
        $success = Set-UserPassword -user $currentUser -password $result.Password
        if ($success) {
            if ([string]::IsNullOrEmpty($result.Password)) {
                [System.Windows.Forms.MessageBox]::Show("Password has been removed. User '$currentUser' can now log in without a password.", "Password Removed", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Information)
            } else {
                [System.Windows.Forms.MessageBox]::Show("Password has been changed.", "Password Change", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Information)
            }
        } else {
            [System.Windows.Forms.MessageBox]::Show("Error setting password. This operation may require administrative privileges.", "Error", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Error)
        }
    } elseif ($result.Action -eq "remove") {
        $success = Remove-UserPassword -user $currentUser
        if ($success) {
            [System.Windows.Forms.MessageBox]::Show("Password has been removed. User '$currentUser' can now log in without a password.", "Password Removed", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Information)
        } else {
            [System.Windows.Forms.MessageBox]::Show("Error removing password. This operation may require administrative privileges.", "Error", [System.Windows.Forms.MessageBoxButtons]::OK, [System.Windows.Forms.MessageBoxIcon]::Error)
        }
    }
    Show-MainMenu
    }

# --- TẠO MENU 2 CỘT, TỰ ĐỘNG CO GIÃN ---
$menuButtons = @(
    @{text='[1] Run All'; action={ [System.Windows.Forms.MessageBox]::Show('Run All!') }},
    @{text='[6] Features'; action={ [System.Windows.Forms.MessageBox]::Show('Turn On Features!') }},
    @{text='[2] Software'; action={ [System.Windows.Forms.MessageBox]::Show('Install All Software!') }},
    @{text='[7] Device'; action={ Invoke-RenameDialog }},
    @{text='[3] Power'; action={ [System.Windows.Forms.MessageBox]::Show('Power Options!') }},
    @{text='[8] Password'; action={ Invoke-SetPasswordDialog }},
    @{text='[4] Volume'; action={ [System.Windows.Forms.MessageBox]::Show('Change/Edit Volume!') }},
    @{text='[9] Domain'; action={ [System.Windows.Forms.MessageBox]::Show('Join Domain!') }},
    @{text='[5] Activate'; action={ [System.Windows.Forms.MessageBox]::Show('Activate!') }},
    @{text='[0] Exit'; action={ $script:form.Close() }}
)

$buttonHeight = 60
$buttonSpacingY = 10
$buttonTop = 80
$buttonLeft = 30
$buttonControls = @()

# Tạo các nút menu
for ($i = 0; $i -lt $menuButtons.Count; $i += 2) {
    # Nút bên trái
    $btnL = New-DynamicButton -text $menuButtons[$i].text -x $buttonLeft -y ($buttonTop + [math]::Floor($i/2)*($buttonHeight+$buttonSpacingY)) -width 1 -height $buttonHeight -clickAction $menuButtons[$i].action
    $btnL.Anchor = [System.Windows.Forms.AnchorStyles]::Top -bor [System.Windows.Forms.AnchorStyles]::Left -bor [System.Windows.Forms.AnchorStyles]::Right
    if ($menuButtons[$i].text -eq '[0] Exit') {
        $btnL.BackColor = [System.Drawing.Color]::FromArgb(200,0,0)
    }
    if ($menuButtons[$i].text -eq '[8] Password') {
        $btnL.Visible = $false
    }
    $script:form.Controls.Add($btnL)
    $buttonControls += $btnL
    # Nút bên phải (nếu có)
    if ($i+1 -lt $menuButtons.Count) {
        $btnR = New-DynamicButton -text $menuButtons[$i+1].text -x 0 -y ($buttonTop + [math]::Floor($i/2)*($buttonHeight+$buttonSpacingY)) -width 1 -height $buttonHeight -clickAction $menuButtons[$i+1].action
        $btnR.Anchor = [System.Windows.Forms.AnchorStyles]::Top -bor [System.Windows.Forms.AnchorStyles]::Left -bor [System.Windows.Forms.AnchorStyles]::Right
        if ($menuButtons[$i+1].text -eq '[0] Exit') {
            $btnR.BackColor = [System.Drawing.Color]::FromArgb(200,0,0)
        }
        if ($menuButtons[$i+1].text -eq '[8] Password') {
            $btnR.Visible = $false
        }
        $script:form.Controls.Add($btnR)
        $buttonControls += $btnR
    }
}

# Cập nhật lại bố cục menu
function Update-MenuLayout {
    $formWidth = $script:form.ClientSize.Width
    $formHeight = $script:form.ClientSize.Height
    $numRows = [math]::Ceiling($buttonControls.Count / 2)
    $minBtnWidth = 120
    $minBtnHeight = 40
    $colWidth = [math]::Max($minBtnWidth, [math]::Floor(($formWidth - 3*$buttonLeft)/2))
    $rowHeight = [math]::Max($minBtnHeight, [math]::Floor(($formHeight - $buttonTop - 30 - ($numRows-1)*$buttonSpacingY) / $numRows))
    for ($i = 0; $i -lt $buttonControls.Count; $i += 2) {
        $rowIdx = [math]::Floor($i/2)
        $y = $buttonTop + $rowIdx*($rowHeight+$buttonSpacingY)
        $buttonControls[$i].Width = $colWidth
        $buttonControls[$i].Height = $rowHeight
        $buttonControls[$i].Left = $buttonLeft
        $buttonControls[$i].Top = $y
        if ($i+1 -lt $buttonControls.Count) {
            $buttonControls[$i+1].Width = $colWidth
            $buttonControls[$i+1].Height = $rowHeight
            $buttonControls[$i+1].Left = 2*$buttonLeft + $colWidth
            $buttonControls[$i+1].Top = $y
        }
    }
}

# Thêm sự kiện resize 
$script:form.Add_Resize({ Update-MenuLayout })
Update-MenuLayout

# Bắt đầu chương trình
$script:form.ShowDialog()